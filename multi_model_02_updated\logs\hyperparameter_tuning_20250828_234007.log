2025-08-28 23:40:07 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-28 23:40:07 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-28 23:40:07 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:40:07 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:40:07 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:40:07 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 60}
2025-08-28 23:40:07 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9225
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9248
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9263
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9277
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9302
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9337
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9402
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9434
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9590
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 19: 发现更好的得分 0.9605
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - Trial 20: 发现更好的得分 0.9661
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9661
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9661
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9661
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9661
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 6, 'min_samples_split': 11, 'min_samples_leaf': 5, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9661
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 实际执行试验次数: 34/50
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:40:08 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250828_234008.html
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250828_234009.html
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.18 秒
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:40:09 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 60}
2025-08-28 23:40:10 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9818
2025-08-28 23:40:10 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9858
2025-08-28 23:40:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9859
2025-08-28 23:40:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9859
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9859
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9859
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 233, 'max_depth': 27, 'min_samples_split': 5, 'min_samples_leaf': 12, 'max_features': 'sqrt'}
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9859
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250828_234019.html
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250828_234019.html
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.19 秒
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:40:19 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\sklearn.py", line 1599, in fit
    self._Booster = train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\training.py", line 181, in train
    bst.update(dtrain, iteration=i, fobj=obj)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 2100, in update
    _check_call(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 284, in _check_call
    raise XGBoostError(py_str(_LIB.XGBGetLastError()))
xgboost.core.XGBoostError: [23:40:19] D:\bld\xgboost-split_1744329010901\work\src\context.cc:239: Both `device` and `gpu_id` are specified. Use `device` instead.

2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9838
2025-08-28 23:40:19 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9856
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9856
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'feature_fraction': 0.9744427686266666, 'bagging_fraction': 0.9828160165372797}
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9856
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250828_234020.html
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250828_234020.html
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.24 秒
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 60}
2025-08-28 23:40:20 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:24 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9808
2025-08-28 23:40:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:26 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9841
2025-08-28 23:40:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:33 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9882
2025-08-28 23:40:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:37 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:46 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:48 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:52 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:40:59 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:41:01 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:41:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:41:05 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9882
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:41:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250828_234107.html
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250828_234107.html
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 47.36 秒
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9660
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.2737414284944617, 'clf__solver': 'liblinear'}
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9686
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250828_234108.html
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250828_234108.html
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.69 秒
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9720
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9755
2025-08-28 23:41:08 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 0.897019110614689, 'clf__kernel': 'linear'}
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9796
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250828_234109.html
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250828_234109.html
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.74 秒
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:41:09 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 60}
2025-08-28 23:41:39 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-28 23:41:39 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-28 23:41:39 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9767
2025-08-28 23:42:09 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9767
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/50
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250828_234209.html
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250828_234209.html
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 60.22 秒
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9525
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:42:09 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-28 23:42:15 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9667
2025-08-28 23:42:15 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9702
2025-08-28 23:42:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:42:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0072064442233928435}
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9702
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250828_234225.html
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250828_234225.html
2025-08-28 23:42:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 16.19 秒
