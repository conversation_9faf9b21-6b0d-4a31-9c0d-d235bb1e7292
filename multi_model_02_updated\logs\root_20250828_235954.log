2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 3
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: -1
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 3, 'n_jobs': 1, 'timeout': 60}
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5219
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 144, 'max_depth': 10, 'learning_rate': 0.22227824312530747, 'subsample': 0.7993292420985183, 'colsample_bytree': 0.5780093202212182}
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.5219
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/3
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250828_235954.html
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250828_235954.html
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.49 秒
2025-08-28 23:59:54 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-28 23:59:54 - model_training - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.083 以处理不平衡
2025-08-28 23:59:54 - model_training - INFO - 模型名称: XGBoost
2025-08-28 23:59:54 - model_training - INFO - 准确率: 1.0000
2025-08-28 23:59:54 - model_training - INFO - AUC: 1.0000
2025-08-28 23:59:54 - model_training - INFO - AUPRC: 1.0000
2025-08-28 23:59:54 - model_training - INFO - 混淆矩阵:
2025-08-28 23:59:54 - model_training - INFO - 
[[ 9  0]
 [ 0 11]]
2025-08-28 23:59:54 - model_training - INFO - 
分类报告:
2025-08-28 23:59:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         9
           1       1.00      1.00      1.00        11

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-28 23:59:54 - model_training - INFO - 训练时间: 0.13 秒
2025-08-28 23:59:54 - model_training - INFO - 模型 XGBoost 性能: 准确率=1.0000
2025-08-28 23:59:54 - model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-28 23:59:54 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_feature_names.joblib
