2025-08-29 00:05:25 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 00:05:25 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 00:05:25 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 00:05:26 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\MM01U\multi_model_02_updated\output\data_exploration
2025-08-29 00:05:26 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-29 00:05:26 - GUI - INFO - GUI界面初始化完成
2025-08-29 00:05:57 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 00:05:57 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 60}
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9303
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9427
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9529
2025-08-29 00:05:57 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9595
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9595
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9595
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9595
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9595
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 40, 'min_samples_leaf': 5, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9595
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/50
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_000558.html
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_000558.html
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.96 秒
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:05:58 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 60}
2025-08-29 00:06:00 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9776
2025-08-29 00:06:00 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9857
2025-08-29 00:06:04 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9883
2025-08-29 00:06:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:06:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-29 00:06:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:06:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 181, 'max_depth': 22, 'min_samples_split': 11, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/50
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250829_000616.html
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250829_000616.html
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.46 秒
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 00:06:16 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9906
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9923
2025-08-29 00:06:17 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:18 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 294, 'max_depth': 7, 'learning_rate': 0.10264378756176612, 'subsample': 0.5089809378074099, 'colsample_bytree': 0.5072567334400258}
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9924
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250829_000620.html
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250829_000620.html
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.73 秒
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 00:06:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9838
2025-08-29 00:06:21 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9856
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9856
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'feature_fraction': 0.9744427686266666, 'bagging_fraction': 0.9828160165372797}
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9856
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250829_000622.html
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250829_000622.html
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.70 秒
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 60}
2025-08-29 00:06:22 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:26 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9808
2025-08-29 00:06:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:28 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9841
2025-08-29 00:06:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:32 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:35 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9882
2025-08-29 00:06:35 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:39 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:42 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:06:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:07:00 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:07:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:07:05 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:07:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9882
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250829_000710.html
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250829_000710.html
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 47.93 秒
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9660
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.9045225075818317, 'clf__solver': 'lbfgs'}
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9669
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:07:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250829_000710.html
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250829_000711.html
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.72 秒
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.193784416824426, 'clf__kernel': 'linear'}
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9787
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250829_000711.html
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250829_000711.html
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.64 秒
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:07:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 60}
2025-08-29 00:07:41 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 00:07:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 00:07:41 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9767
2025-08-29 00:08:11 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9767
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/50
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_000811.html
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_000811.html
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 60.16 秒
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9525
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:08:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-29 00:08:17 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9667
2025-08-29 00:08:23 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9702
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:08:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.007373630278693539}
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9702
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 实际执行试验次数: 25/50
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_000835.html
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_000835.html
2025-08-29 00:08:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 23.74 秒
