2025-08-28 17:07:12 - model_training - INFO - 模型名称: Random Forest
2025-08-28 17:07:12 - model_training - INFO - 准确率: 0.8500
2025-08-28 17:07:12 - model_training - INFO - AUC: 0.9501
2025-08-28 17:07:12 - model_training - INFO - AUPRC: 0.9410
2025-08-28 17:07:12 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:12 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-28 17:07:12 - model_training - INFO - 
分类报告:
2025-08-28 17:07:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-28 17:07:12 - model_training - INFO - 训练时间: 0.22 秒
2025-08-28 17:07:12 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-28 17:07:12 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\RandomForest_single_170712.joblib
2025-08-28 17:07:12 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-28 17:07:12 - model_training - WARNING - [XGBoost] PyTorch未安装，无法检测GPU，使用CPU模式
2025-08-28 17:07:12 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:07:13 - model_training - INFO - 模型名称: XGBoost
2025-08-28 17:07:13 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:07:13 - model_training - INFO - AUC: 0.9437
2025-08-28 17:07:13 - model_training - INFO - AUPRC: 0.9387
2025-08-28 17:07:13 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:13 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-28 17:07:13 - model_training - INFO - 
分类报告:
2025-08-28 17:07:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-28 17:07:13 - model_training - INFO - 训练时间: 0.06 秒
2025-08-28 17:07:13 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-28 17:07:13 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\XGBoost_single_170713.joblib
2025-08-28 17:07:13 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-28 17:07:13 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:07:13 - model_training - INFO - 模型名称: LightGBM
2025-08-28 17:07:13 - model_training - INFO - 准确率: 0.8750
2025-08-28 17:07:13 - model_training - INFO - AUC: 0.9642
2025-08-28 17:07:13 - model_training - INFO - AUPRC: 0.9541
2025-08-28 17:07:13 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:13 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-28 17:07:13 - model_training - INFO - 
分类报告:
2025-08-28 17:07:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-28 17:07:13 - model_training - INFO - 训练时间: 0.09 秒
2025-08-28 17:07:13 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-28 17:07:13 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\LightGBM_single_170713.joblib
2025-08-28 17:07:13 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-28 17:07:13 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-28 17:07:14 - model_training - INFO - 模型名称: CatBoost
2025-08-28 17:07:14 - model_training - INFO - 准确率: 0.8000
2025-08-28 17:07:14 - model_training - INFO - AUC: 0.9540
2025-08-28 17:07:14 - model_training - INFO - AUPRC: 0.9486
2025-08-28 17:07:14 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:14 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-28 17:07:14 - model_training - INFO - 
分类报告:
2025-08-28 17:07:14 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-28 17:07:14 - model_training - INFO - 训练时间: 1.25 秒
2025-08-28 17:07:14 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-28 17:07:14 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\CatBoost_single_170714.joblib
2025-08-28 17:07:14 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型名称: Logistic Regression
2025-08-28 17:07:16 - model_training - INFO - 准确率: 0.7750
2025-08-28 17:07:16 - model_training - INFO - AUC: 0.9028
2025-08-28 17:07:16 - model_training - INFO - AUPRC: 0.8685
2025-08-28 17:07:16 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:16 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-28 17:07:16 - model_training - INFO - 
分类报告:
2025-08-28 17:07:16 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-28 17:07:16 - model_training - INFO - 训练时间: 2.23 秒
2025-08-28 17:07:16 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-28 17:07:16 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\Logistic_single_170716.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型名称: SVM
2025-08-28 17:07:16 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:07:16 - model_training - INFO - AUC: 0.9258
2025-08-28 17:07:16 - model_training - INFO - AUPRC: 0.9259
2025-08-28 17:07:16 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:16 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-28 17:07:16 - model_training - INFO - 
分类报告:
2025-08-28 17:07:16 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-28 17:07:16 - model_training - INFO - 训练时间: 0.01 秒
2025-08-28 17:07:16 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-28 17:07:16 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\SVM_single_170716.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\SVM_results.joblib
2025-08-28 17:23:39 - model_training - INFO - 模型名称: KNN
2025-08-28 17:23:39 - model_training - INFO - 准确率: 0.8000
2025-08-28 17:23:39 - model_training - INFO - AUC: 0.9054
2025-08-28 17:23:39 - model_training - INFO - AUPRC: 0.8760
2025-08-28 17:23:39 - model_training - INFO - 混淆矩阵:
2025-08-28 17:23:39 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-28 17:23:39 - model_training - INFO - 
分类报告:
2025-08-28 17:23:39 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-28 17:23:39 - model_training - INFO - 训练时间: 982.64 秒
2025-08-28 17:23:39 - model_training - INFO - 模型 KNN 性能: 准确率=0.8000
2025-08-28 17:23:39 - model_training - INFO - 模型 KNN 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\KNN_single_172339.joblib
2025-08-28 17:23:39 - model_training - INFO - 模型 KNN 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\KNN_results.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型名称: Random Forest
2025-08-28 17:50:51 - model_training - INFO - 准确率: 0.8500
2025-08-28 17:50:51 - model_training - INFO - AUC: 0.9501
2025-08-28 17:50:51 - model_training - INFO - AUPRC: 0.9410
2025-08-28 17:50:51 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:51 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-28 17:50:51 - model_training - INFO - 
分类报告:
2025-08-28 17:50:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-28 17:50:51 - model_training - INFO - 训练时间: 0.19 秒
2025-08-28 17:50:51 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-28 17:50:51 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\RandomForest_single_175051.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-28 17:50:51 - model_training - WARNING - [XGBoost] PyTorch未安装，无法检测GPU，使用CPU模式
2025-08-28 17:50:51 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:50:51 - model_training - INFO - 模型名称: XGBoost
2025-08-28 17:50:51 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:50:51 - model_training - INFO - AUC: 0.9437
2025-08-28 17:50:51 - model_training - INFO - AUPRC: 0.9387
2025-08-28 17:50:51 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:51 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-28 17:50:51 - model_training - INFO - 
分类报告:
2025-08-28 17:50:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-28 17:50:51 - model_training - INFO - 训练时间: 0.05 秒
2025-08-28 17:50:51 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-28 17:50:51 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\XGBoost_single_175051.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-28 17:50:51 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:50:51 - model_training - INFO - 模型名称: LightGBM
2025-08-28 17:50:51 - model_training - INFO - 准确率: 0.8750
2025-08-28 17:50:51 - model_training - INFO - AUC: 0.9642
2025-08-28 17:50:51 - model_training - INFO - AUPRC: 0.9541
2025-08-28 17:50:51 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:51 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-28 17:50:51 - model_training - INFO - 
分类报告:
2025-08-28 17:50:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-28 17:50:51 - model_training - INFO - 训练时间: 0.08 秒
2025-08-28 17:50:51 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-28 17:50:51 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\LightGBM_single_175051.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-28 17:50:51 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-28 17:50:53 - model_training - INFO - 模型名称: CatBoost
2025-08-28 17:50:53 - model_training - INFO - 准确率: 0.8000
2025-08-28 17:50:53 - model_training - INFO - AUC: 0.9540
2025-08-28 17:50:53 - model_training - INFO - AUPRC: 0.9486
2025-08-28 17:50:53 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:53 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-28 17:50:53 - model_training - INFO - 
分类报告:
2025-08-28 17:50:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-28 17:50:53 - model_training - INFO - 训练时间: 1.14 秒
2025-08-28 17:50:53 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-28 17:50:53 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\CatBoost_single_175053.joblib
2025-08-28 17:50:53 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型名称: Logistic Regression
2025-08-28 17:50:55 - model_training - INFO - 准确率: 0.7750
2025-08-28 17:50:55 - model_training - INFO - AUC: 0.9028
2025-08-28 17:50:55 - model_training - INFO - AUPRC: 0.8685
2025-08-28 17:50:55 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:55 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-28 17:50:55 - model_training - INFO - 
分类报告:
2025-08-28 17:50:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-28 17:50:55 - model_training - INFO - 训练时间: 2.33 秒
2025-08-28 17:50:55 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-28 17:50:55 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\Logistic_single_175055.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型名称: SVM
2025-08-28 17:50:55 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:50:55 - model_training - INFO - AUC: 0.9258
2025-08-28 17:50:55 - model_training - INFO - AUPRC: 0.9259
2025-08-28 17:50:55 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:55 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-28 17:50:55 - model_training - INFO - 
分类报告:
2025-08-28 17:50:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-28 17:50:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-28 17:50:55 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-28 17:50:55 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\SVM_single_175055.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\SVM_results.joblib
2025-08-28 23:59:54 - model_training - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.083 以处理不平衡
2025-08-28 23:59:54 - model_training - INFO - 模型名称: XGBoost
2025-08-28 23:59:54 - model_training - INFO - 准确率: 1.0000
2025-08-28 23:59:54 - model_training - INFO - AUC: 1.0000
2025-08-28 23:59:54 - model_training - INFO - AUPRC: 1.0000
2025-08-28 23:59:54 - model_training - INFO - 混淆矩阵:
2025-08-28 23:59:54 - model_training - INFO - 
[[ 9  0]
 [ 0 11]]
2025-08-28 23:59:54 - model_training - INFO - 
分类报告:
2025-08-28 23:59:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         9
           1       1.00      1.00      1.00        11

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-28 23:59:54 - model_training - INFO - 训练时间: 0.13 秒
2025-08-28 23:59:54 - model_training - INFO - 模型 XGBoost 性能: 准确率=1.0000
2025-08-28 23:59:54 - model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-28 23:59:54 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_feature_names.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.8500
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.8760
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.7701
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8500
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\DecisionTree_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型名称: Random Forest
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9000
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9655
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9497
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[19  4]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.83      0.90        23
           1       0.81      1.00      0.89        17

    accuracy                           0.90        40
   macro avg       0.90      0.91      0.90        40
weighted avg       0.92      0.90      0.90        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\RandomForest_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 00:53:11 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 00:53:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 00:53:11 - model_training - INFO - 模型名称: XGBoost
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9000
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9565
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9248
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[19  4]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.83      0.90        23
           1       0.81      1.00      0.89        17

    accuracy                           0.90        40
   macro avg       0.90      0.91      0.90        40
weighted avg       0.92      0.90      0.90        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\XGBoost_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 00:53:11 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 00:53:11 - model_training - INFO - 模型名称: LightGBM
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9500
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9770
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9608
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[21  2]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.91      0.95        23
           1       0.89      1.00      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.96      0.95        40
weighted avg       0.96      0.95      0.95        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.14 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9500
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\LightGBM_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 00:53:11 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 00:53:11 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 00:53:29 - model_training - INFO - 模型名称: CatBoost
2025-08-29 00:53:29 - model_training - INFO - 准确率: 0.9500
2025-08-29 00:53:29 - model_training - INFO - AUC: 0.9923
2025-08-29 00:53:29 - model_training - INFO - AUPRC: 0.9896
2025-08-29 00:53:29 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:29 - model_training - INFO - 
[[21  2]
 [ 0 17]]
2025-08-29 00:53:29 - model_training - INFO - 
分类报告:
2025-08-29 00:53:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.91      0.95        23
           1       0.89      1.00      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.96      0.95        40
weighted avg       0.96      0.95      0.95        40

2025-08-29 00:53:29 - model_training - INFO - 训练时间: 17.99 秒
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9500
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\CatBoost_single_005329.joblib
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 00:53:29 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 00:53:29 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 00:53:29 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 00:53:29 - model_training - INFO - 准确率: 0.8750
2025-08-29 00:53:29 - model_training - INFO - AUC: 0.9642
2025-08-29 00:53:29 - model_training - INFO - AUPRC: 0.9582
2025-08-29 00:53:29 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:29 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-29 00:53:29 - model_training - INFO - 
分类报告:
2025-08-29 00:53:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-29 00:53:30 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\Logistic_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 00:53:30 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 00:53:30 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 00:53:30 - model_training - INFO - 模型名称: SVM
2025-08-29 00:53:30 - model_training - INFO - 准确率: 0.8500
2025-08-29 00:53:30 - model_training - INFO - AUC: 0.9514
2025-08-29 00:53:30 - model_training - INFO - AUPRC: 0.9405
2025-08-29 00:53:30 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:30 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 00:53:30 - model_training - INFO - 
分类报告:
2025-08-29 00:53:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 00:53:30 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 性能: 准确率=0.8500
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\SVM_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 00:53:30 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 00:53:30 - model_training - INFO - [KNN] 使用Pipeline进行标准化
