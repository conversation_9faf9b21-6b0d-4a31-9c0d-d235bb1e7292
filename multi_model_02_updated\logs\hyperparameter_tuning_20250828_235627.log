2025-08-28 23:56:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:56:27 - hyperparameter_tuning - ERROR - XGBoost目标函数出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\sklearn.py", line 1599, in fit
    self._Booster = train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\training.py", line 181, in train
    bst.update(dtrain, iteration=i, fobj=obj)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 2100, in update
    _check_call(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 284, in _check_call
    raise XGBoostError(py_str(_LIB.XGBGetLastError()))
xgboost.core.XGBoostError: [23:56:27] D:\bld\xgboost-split_1744329010901\work\src\context.cc:239: Both `device` and `gpu_id` are specified. Use `device` instead.

2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 3
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: -1
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 3, 'n_jobs': 1, 'timeout': 60}
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:56:27 - hyperparameter_tuning - ERROR - XGBoost目标函数出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\sklearn.py", line 1599, in fit
    self._Booster = train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\training.py", line 181, in train
    bst.update(dtrain, iteration=i, fobj=obj)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 2100, in update
    _check_call(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 284, in _check_call
    raise XGBoostError(py_str(_LIB.XGBGetLastError()))
xgboost.core.XGBoostError: [23:56:27] D:\bld\xgboost-split_1744329010901\work\src\context.cc:239: Both `device` and `gpu_id` are specified. Use `device` instead.

2025-08-28 23:56:27 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.0000
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:56:27 - hyperparameter_tuning - ERROR - XGBoost目标函数出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\sklearn.py", line 1599, in fit
    self._Booster = train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\training.py", line 181, in train
    bst.update(dtrain, iteration=i, fobj=obj)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 2100, in update
    _check_call(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 284, in _check_call
    raise XGBoostError(py_str(_LIB.XGBGetLastError()))
xgboost.core.XGBoostError: [23:56:27] D:\bld\xgboost-split_1744329010901\work\src\context.cc:239: Both `device` and `gpu_id` are specified. Use `device` instead.

2025-08-28 23:56:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:56:27 - hyperparameter_tuning - ERROR - XGBoost目标函数出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\sklearn.py", line 1599, in fit
    self._Booster = train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 726, in inner_f
    return func(**kwargs)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\training.py", line 181, in train
    bst.update(dtrain, iteration=i, fobj=obj)
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 2100, in update
    _check_call(
  File "D:\anaconda\envs\multi_model\lib\site-packages\xgboost\core.py", line 284, in _check_call
    raise XGBoostError(py_str(_LIB.XGBGetLastError()))
xgboost.core.XGBoostError: [23:56:27] D:\bld\xgboost-split_1744329010901\work\src\context.cc:239: Both `device` and `gpu_id` are specified. Use `device` instead.

2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 144, 'max_depth': 10, 'learning_rate': 0.22227824312530747, 'subsample': 0.7993292420985183, 'colsample_bytree': 0.5780093202212182}
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.0000
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/3
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250828_235627.html
2025-08-28 23:56:27 - hyperparameter_tuning - WARNING - 绘制超参数重要性图失败: Encountered zero total variance in all trees.
2025-08-28 23:56:27 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.21 秒
