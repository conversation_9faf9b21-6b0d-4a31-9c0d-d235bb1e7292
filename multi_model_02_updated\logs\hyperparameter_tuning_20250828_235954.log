2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 3
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: -1
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 3, 'n_jobs': 1, 'timeout': 60}
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5219
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 144, 'max_depth': 10, 'learning_rate': 0.22227824312530747, 'subsample': 0.7993292420985183, 'colsample_bytree': 0.5780093202212182}
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.5219
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/3
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250828_235954.html
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250828_235954.html
2025-08-28 23:59:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.49 秒
