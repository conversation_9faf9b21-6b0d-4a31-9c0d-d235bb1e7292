2025-08-29 00:42:45 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 00:42:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 00:42:46 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 00:42:46 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\MM01U\multi_model_02_updated\output\data_exploration
2025-08-29 00:42:46 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-29 00:42:47 - GUI - INFO - GUI界面初始化完成
2025-08-29 00:44:46 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 00:44:46 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 60}
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9266
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9597
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9597
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9597
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9597
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9597
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 16, 'min_samples_leaf': 5, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9597
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_004447.html
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_004447.html
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.68 秒
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:44:47 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 60}
2025-08-29 00:44:49 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9684
2025-08-29 00:44:52 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9718
2025-08-29 00:44:52 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9749
2025-08-29 00:44:57 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9782
2025-08-29 00:45:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:45:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9782
2025-08-29 00:45:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:45:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9782
2025-08-29 00:45:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:45:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9782
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9782
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 51, 'max_depth': 32, 'min_samples_split': 20, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9782
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250829_004502.html
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250829_004502.html
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.47 秒
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9767
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9784
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9801
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:03 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9824
2025-08-29 00:45:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:05 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:05 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:05 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9825
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 136, 'max_depth': 5, 'learning_rate': 0.1043543994116726, 'subsample': 0.8918148105745085, 'colsample_bytree': 0.6898021217101118}
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9825
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250829_004506.html
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250829_004506.html
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.24 秒
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9783
2025-08-29 00:45:06 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9817
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 55, 'max_depth': 10, 'learning_rate': 0.2514083658321223, 'feature_fraction': 0.6061695553391381, 'bagging_fraction': 0.5909124836035503}
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9817
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250829_004507.html
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250829_004507.html
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.00 秒
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 60}
2025-08-29 00:45:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:11 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9704
2025-08-29 00:45:11 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:13 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9744
2025-08-29 00:45:13 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:15 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:17 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9802
2025-08-29 00:45:17 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:20 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:32 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:38 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:40 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:46 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9835
2025-08-29 00:45:46 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:52 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:45:56 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:46:01 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:46:04 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:46:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 277, 'depth': 6, 'learning_rate': 0.16911754293924608, 'l2_leaf_reg': 4.165445138583031, 'bagging_temperature': 0.0041461527401535775}
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9835
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250829_004609.html
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250829_004609.html
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 62.06 秒
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9552
2025-08-29 00:46:09 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9569
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9585
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - Trial 14: 发现更好的得分 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 0.11130975281549899, 'clf__solver': 'lbfgs'}
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9601
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 29/50
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250829_004610.html
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250829_004610.html
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.28 秒
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:46:10 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9554
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 3.2652332192656557, 'clf__kernel': 'linear'}
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9556
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250829_004611.html
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250829_004611.html
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.71 秒
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:46:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 60}
2025-08-29 00:46:41 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 00:46:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 00:46:41 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9453
2025-08-29 00:47:11 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9453
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/50
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_004711.html
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_004711.html
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 60.23 秒
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9408
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 超时设置: 60秒
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 00:47:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 60}
2025-08-29 00:47:17 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9575
2025-08-29 00:47:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:47:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9575
2025-08-29 00:47:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:47:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9575
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9575
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9575
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9575
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9575
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0023706167111668193}
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9575
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_004728.html
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_004728.html
2025-08-29 00:47:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 16.83 秒
2025-08-29 00:53:11 - training_session_manager - INFO - 创建会话目录结构: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311
2025-08-29 00:53:11 - training_session_manager - INFO - 创建训练会话: 训练_N-2_20250829_005311 (ID: 20250829_005311)
2025-08-29 00:53:11 - training_session_manager - INFO - 创建新会话: 训练_N-2_20250829_005311
2025-08-29 00:53:11 - session_utils - INFO - 创建新会话: 训练_N-2_20250829_005311 (ID: 20250829_005311)
2025-08-29 00:53:11 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 00:53:11 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 00:53:11 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.8500
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.8760
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.7701
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8500
2025-08-29 00:53:11 - training_session_manager - INFO - 保存模型 DecisionTree 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\DecisionTree_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\DecisionTree_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型名称: Random Forest
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9000
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9655
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9497
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[19  4]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.83      0.90        23
           1       0.81      1.00      0.89        17

    accuracy                           0.90        40
   macro avg       0.90      0.91      0.90        40
weighted avg       0.92      0.90      0.90        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-29 00:53:11 - training_session_manager - INFO - 保存模型 RandomForest 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\RandomForest_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\RandomForest_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 00:53:11 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 00:53:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 00:53:11 - model_training - INFO - 模型名称: XGBoost
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9000
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9565
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9248
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[19  4]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.83      0.90        23
           1       0.81      1.00      0.89        17

    accuracy                           0.90        40
   macro avg       0.90      0.91      0.90        40
weighted avg       0.92      0.90      0.90        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-29 00:53:11 - training_session_manager - INFO - 保存模型 XGBoost 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\XGBoost_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\XGBoost_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 00:53:11 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 00:53:11 - model_training - INFO - 模型名称: LightGBM
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9500
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9770
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9608
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[21  2]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.91      0.95        23
           1       0.89      1.00      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.96      0.95        40
weighted avg       0.96      0.95      0.95        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.14 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9500
2025-08-29 00:53:11 - training_session_manager - INFO - 保存模型 LightGBM 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\LightGBM_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\LightGBM_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 00:53:11 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 00:53:11 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 00:53:29 - model_training - INFO - 模型名称: CatBoost
2025-08-29 00:53:29 - model_training - INFO - 准确率: 0.9500
2025-08-29 00:53:29 - model_training - INFO - AUC: 0.9923
2025-08-29 00:53:29 - model_training - INFO - AUPRC: 0.9896
2025-08-29 00:53:29 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:29 - model_training - INFO - 
[[21  2]
 [ 0 17]]
2025-08-29 00:53:29 - model_training - INFO - 
分类报告:
2025-08-29 00:53:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.91      0.95        23
           1       0.89      1.00      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.96      0.95        40
weighted avg       0.96      0.95      0.95        40

2025-08-29 00:53:29 - model_training - INFO - 训练时间: 17.99 秒
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9500
2025-08-29 00:53:29 - training_session_manager - INFO - 保存模型 CatBoost 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\CatBoost_single_005329.joblib
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\CatBoost_single_005329.joblib
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 00:53:29 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 00:53:29 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 00:53:29 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 00:53:29 - model_training - INFO - 准确率: 0.8750
2025-08-29 00:53:29 - model_training - INFO - AUC: 0.9642
2025-08-29 00:53:29 - model_training - INFO - AUPRC: 0.9582
2025-08-29 00:53:29 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:29 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-29 00:53:29 - model_training - INFO - 
分类报告:
2025-08-29 00:53:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-29 00:53:30 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-29 00:53:30 - training_session_manager - INFO - 保存模型 Logistic 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\Logistic_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\Logistic_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 00:53:30 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 00:53:30 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 00:53:30 - model_training - INFO - 模型名称: SVM
2025-08-29 00:53:30 - model_training - INFO - 准确率: 0.8500
2025-08-29 00:53:30 - model_training - INFO - AUC: 0.9514
2025-08-29 00:53:30 - model_training - INFO - AUPRC: 0.9405
2025-08-29 00:53:30 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:30 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 00:53:30 - model_training - INFO - 
分类报告:
2025-08-29 00:53:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 00:53:30 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 性能: 准确率=0.8500
2025-08-29 00:53:30 - training_session_manager - INFO - 保存模型 SVM 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\SVM_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\SVM_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 00:53:30 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 00:53:30 - model_training - INFO - [KNN] 使用Pipeline进行标准化
